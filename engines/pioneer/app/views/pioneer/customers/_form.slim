.panel.panel-default
  = form_for @customer, url: url, html: { multipart: true } do |f|
    .panel-body
      .col-md-12
        .row
          .col-md-6
            .form-group
              - stores = current_user.has_role?('administrator') ? Mkp::Store.all : [current_user.role.store]
              = f.select(:store_id, stores.map{|r|[r.name, r.id]}, {}, {:class => 'form-control'}) unless @customer.persisted?
        .row
          .col-md-6
            .form-group
              = f.text_field :first_name, required: true, class: 'form-control'
          .col-md-6
            .form-group
              = f.text_field :last_name, required: true, class: 'form-control'
        .row
          .col-md-6
            .form-group
              = f.select(:doc_type, Mkp::Customer.doc_types.keys, {}, { :class => 'form-control' })
          .col-md-6
            .form-group
              = f.text_field :doc_number, required: true, class: 'form-control'
        .row
          .col-md-6
            .form-group
              = f.text_field :telephone, required: true, class: 'form-control'
          .col-md-6
            .form-group
              = f.email_field :email, required: true, class: 'form-control'
        .row
          .col-md-6
            .form-group
              = f.select(:gender, Mkp::Customer.genders.keys, {}, { :class => 'form-control' })
          .col-md-6
            .form-group
              = f.text_field :birthday_at, class: 'form-control'
        .row
          - if @customer.id.present?
            .col-md-6
              .form-group
                = f.text_field :validator, class: 'form-control'
          .col-md-6
            .form-group
              = f.text_field :cuit, class: 'form-control'
        .row
          .col-md-6
            .form-group
              = f.text_field :password, required: !@customer.persisted?, class: 'form-control'
          .col-md-6
            .form-group
              = f.text_field :password_confirmation, required: !@customer.persisted?, class: 'form-control'
    .panel-body
      .col-md-12
        = f.fields_for :member do |m|
          .row
            .col-md-12
              h3 Dato del Socio
              = m.hidden_field :id, required: true, class: 'form-control'
            .col-md-6
              .form-group
                = m.text_field :nationality, required: true, class: 'form-control'
            .col-md-6
              .form-group
                = m.text_field :mobile, required: true, class: 'form-control'
          .row
            .col-md-6
              .form-group
                = m.text_field :origin, required: true, class: 'form-control'
            .col-md-6
              .form-group
                = m.select(:enabled, [true, false], {}, { :class => 'form-control' })
    .panel-body
      .col-md-12
        = f.fields_for :addresses do |address|
          = render 'address_fields', f: address
        .links
          hr
          = link_to_add_association '+ Agregar Direccion', f, :addresses, class: 'btn btn-success'
        hr
      .large-4.pull-right.columns
        = f.submit (@customer.persisted? ? 'Update' : 'Create'), class: "btn small btn-primary"
