module Pioneer
  class CustomersController < Pioneer::ApplicationController
    #load_and_authorize_resource class: Mkp::Customer
    #skip_before_filter :authenticate_admin_role!
    before_filter :find_customer, only: %i[new edit update destroy]
    before_filter :find_customers, only: [:index]
    before_filter :have_permissions_to_read, except: [:index, :find_customer, :customer_params]
    before_filter :have_permissions_to_write, except: [:index, :find_customer, :customer_params]
    layout 'bootstrap_layout'

    def index
      @customers_to_csv = @filterrific.find
      respond_to do |format|
        format.html
        format.csv { send_data Tools::Export.to_csv(Mkp::Customer.attribute_names, @customers_to_csv), filename: "Datos Registrados-#{DateTime.now}.csv" }
      end
    end

    def per_page
      params[:format] == 'csv' ? 1000000 : 50
    end

    def new; end

    def create
      @customer = Mkp::Customer.unscoped.deleted
                  .where(doc_number: customer_params[:doc_number])
                  .or(Mkp::Customer.unscoped.deleted.where(email: customer_params[:email])).first

      if !@customer
        @customer = Mkp::Customer.new(customer_params)
        if @customer.save
          redirect_to customers_path
        else
          render :new
        end
      else
        if @customer.update(customer_params.merge(deleted_at: nil))
          redirect_to customers_path
        else
          render :new
        end
      end
    end

    def edit; end

    def update
      @customer.update_attributes(customer_params)
      if @customer.valid?
        flash[:success] = "Customer Update"
        redirect_to customers_path
      else
        flash[:error] = @customer.errors.full_messages
        render :edit
      end
    end

    def destroy
      if @customer.destroy
        head :ok
      else
        redirect_to customers_path
      end
    end

    def restore
      @customer = Mkp::Customer.unscoped.find(params[:id])

      if @customer.update(deleted_at: nil)
        render json: { success: true }, status: :ok
      else
        render json: { success: false, errors: @customer.errors.full_messages }, status: :unprocessable_entity
      end
    end

    private

    def find_customers
      @store_ids = current_user.has_role?(:administrator) ? Mkp::Store.all.ids : current_user.role.store_id
      @stores = Mkp::Store.where(id: @store_ids)

      @filterrific = initialize_filterrific(
        Mkp::Customer,
        params[:filterrific],
        select_options: { with_store_id: @stores.options_for_select },
        default_filter_params: { with_store_id: @stores.pluck(:id) }
      ) || return

      @customers = @filterrific.find.page(params[:page]).order(created_at: :desc)
    end

    def customer_params
      params.require(:mkp_customer).permit(:email, :first_name, :last_name, :current_password, :password, :password_confirmation, :image, :doc_type, :doc_number, :gender, :birthday_at, :telephone, :store_id, :validator, :cuit,
            member_attributes: [ :id, :nationality, :mobile, :origin, :enabled, :_destroy ],
            addresses_attributes: [ :id, :internal_country_address,
                                    :first_name, :last_name, :telephone,
                                    :doc_type, :doc_number,
                                    :address, :address_2, :city,
                                    :state, :country, :zip, :street_number,
                                    :floor, :dpto, :tower, :body, :lateral_street_1,
                                    :lateral_street_2, :county, :country_club, :_destroy
                                  ])
    end

    def find_customer
      @customer = Mkp::Customer.not_deleted.find_by_id(params[:id]) || Mkp::Customer.new
    end

    def have_permissions_to_read
      redirect_to home_url, :alert => "Have not permissions" if cannot?(:read, "Customer")
    end

    def have_permissions_to_write
      redirect_to customers_path, :alert => "Have not permissions" if cannot?(:crud, "Customer")
    end
  end
end
