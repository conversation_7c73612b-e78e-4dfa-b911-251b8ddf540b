module VariantGrouping
  extend ActiveSupport::Concern

  private

  def group_variants_by_product(variants)
    grouped_variants = variants.group_by(&:product_id)

    representative_variants = grouped_variants.map do |product_id, product_variants|
      representative_variant = product_variants.find { |v| v.picture.present? } ||
                              product_variants.max_by(&:quantity) ||
                              product_variants.first

      representative_variant.define_singleton_method(:available_variants) do
        product_variants
      end

      representative_variant
    end

    representative_variants.paginate(
      page: variants.current_page,
      per_page: variants.per_page,
      total_entries: variants.total_entries
    )
  end

  def create_paginated_collection(items, page = 1, per_page = nil)
    per_page ||= items.length
    items.paginate(
      page: page,
      per_page: per_page,
      total_entries: items.length
    )
  end
end
