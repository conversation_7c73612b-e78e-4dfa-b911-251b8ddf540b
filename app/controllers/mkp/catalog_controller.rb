module Mkp
  class CatalogController < ApplicationController
    include CatalogLegacy
    include VariantGrouping

    ID_REGEX = /^[0-9]+$/

    before_filter :ensure_params_value_types

    def index
      # @debug - START
      # In order to debug the scoring for now. We should improve the way this is
      # implemented, but maybe doesn't make sense to have it, and in that
      # case we should remove it, toguether with all the calls to @debug on the
      # views and other libs
      if params[:debug].present?
        params[:debug] == 'score' ? cookies[:debug] = true : cookies.delete(:debug)
      end
      if cookies[:debug].present?
        if cookies[:debug] == 'true' || cookies[:debug] == true
          @debug = true
        else
          cookies.delete(:debug)
        end
      end
      # @debug - END

      if handling_old_url?
        handle_redirect! and return
      end

      @models = retrieve_models(nil, @current_store)

      search_variants

      render_properly :index
    end

    # Legacy endpoint. Please remove on a sensible ETA.
    def legacy
      if Mkp::Catalog::UrlHandler.parse_slugs(params)
        handle_redirect! and return
      else
        not_found
      end
    end

    private

    def ensure_params_value_types
      params.tap do |p|
        p[:b] = p[:b].values if p[:b] && p[:b].is_a?(Hash)
        p[:sp] = p[:sp].values if p[:sp] && p[:sp].is_a?(Hash)
        p[:g] = p[:g].values if p[:g] && p[:g].is_a?(Hash)
      end
    end

    def handling_old_url?
      return false if params[:term].present? # We will assume that if this is the case
                                             # it's a suggester, this is dangerous because
                                             # could be a param via the GET (as a Querystring)
                                             # for example /ar/indumentaria?term=hola
      mapper_params_keys = params.keys.map(&:to_sym) & UrlMapper::CATALOG_MODELS_ORDER.values

      if params[UrlMapper::PATH_KEY].present?
        params.values_at(*mapper_params_keys).any? do |value|
          if value.is_a?(Array) && value.length == 1
            return true
          else
            value.is_a?(String)
          end
        end
      else
        mapper_params_keys.present? && some_param_value_is_invalid?(mapper_params_keys)
      end
    end
    def some_param_value_is_invalid?(mapper_params_keys)
      mapper_params_keys.any? do |key|
        (params[key].is_a?(Array) && params[key].length == 1) || params[key].is_a?(String)
      end
    end

    def search_variants
      if params[:term].blank? && params[:query].present?
        increase_suggestion_count
      end

      if params[:query].blank? && params[:o].blank?
        @seed = (params[:s] || Random.rand(999).to_s(36))
        params[:s] = @seed if params[:s].blank?
      end

      search = Mkp::Catalog::Finder.find(normalized_params, { debug: @debug })
      @search = search if @debug
      @variants = group_variants_by_product(search.results)

      @breadcrumb = build_breadcrumb
      @filter = Mkp::Catalog::Filter.new(search, normalized_params, @models).arrange

      if @variants.empty?
        @recommended_variants = \
          Mkp::Variant.with_stock.active.visibles.by_network(@network).random(16)
      end

      set_google_tag_params
    end

    def normalized_params
      other_params = HashWithIndifferentAccess.new(params.dup)

      other_params.reject! do |key, value|
        unless value.respond_to?(:to_ary)
          value.blank?
        else
          value.all?(&:blank?)
        end
      end

      if params[:b].is_a?(Array)
        other_params[:b] = get_manufacturers.map(&:id)
      end

      if params[:sp].is_a?(Array)
        other_params[:sp] = get_sports.map(&:id)
      end

      other_params[:store_id] = @current_store.id
      other_params
    end

    def set_google_tag_params
      @tag_params = {
        ecomm_pagetype: "category",
        ecomm_network: @network
      }

      @tag_params[:ecomm_prodid] = @variants.map(&:id) if @variants.present?
      @tag_params[:ecomm_filterbygender] = params[:g] if params[:g].present?
      @tag_params[:ecomm_filterbybrand] = params[:b] if params[:b].present?
      @tag_params[:ecomm_filterbyspost] = params[:sp] if params[:sp].present?
      @tag_params[:ecomm_filterbycategory] = params[:c] if params[:c].present?
      @tag_params[:ecomm_page] = @variants.current_page if @variants.present?
      @tag_params[:ecomm_totalpages] = @variants.total_pages if @variants.present?
      @tag_params[:ecomm_query] = params[:query] if params[:query].present?

      @tag_params = @tag_params.to_json
    end

    def build_breadcrumb
      parameters = HashWithIndifferentAccess.new(params.select do |key|
        %w(network controller action).include?(key)
      end)

      network = parameters[:network].downcase

      breadcrumb = [
        {
          name: 'Shop',
          href: Rails.application.routes.url_helpers.mkp_catalog_root_path(network: network)
        }
      ]

      models = []

      if (gender = @genders&.last).present?

        models << gender

        breadcrumb << {
         name: gender.name,
         href: UrlMapperHelper.absolute_mapper_path(models, network)
        }
      end

      if @sports.present? && @sports.size == 1
        sport = @sports.first

        models << sport

        breadcrumb << {
          name: sport.name,
          href: UrlMapperHelper.absolute_mapper_path(models, network)
        }
      end

      if @categories.present?
        @categories.first.path.each do |category|

          category_models = models + [category]

          breadcrumb << {
            name: category.name,
            href: UrlMapperHelper.absolute_mapper_path(category_models, network)
          }
        end
        models += @categories
      end

      if @manufacturers.present? && @manufacturers.size == 1
        manufacturer = @manufacturers.first

        breadcrumb << {
          name: manufacturer.name,
          href: UrlMapperHelper.absolute_mapper_path(models + [manufacturer], network),
          href_remove: UrlMapperHelper.absolute_mapper_path(models, network)
        }
      end
      if params[:d].present?
        breadcrumb << {
          name:  I18n.t('mkp.catalog.v5.filter.sale.on_sale'),
          href: UrlMapperHelper.absolute_mapper_path(models, network, {d: 1}),
          href_remove: UrlMapperHelper.absolute_mapper_path(models, network)
        }
      end

      if params[:query].present? && params[:term].blank?
        breadcrumb << {
          query: true,
          name: params[:query]
        }
      end

      breadcrumb
    end

    def increase_suggestion_count
      if (suggestion = Suggestion.find_by_term(params[:query])).present?
        suggestion.update_attributes(count: suggestion.count + 1)
      else
        Suggestion.create({
          term: params[:query].downcase,
          network: @network.downcase
        })
      end
    end
  end
end
