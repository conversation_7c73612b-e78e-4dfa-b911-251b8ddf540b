class Api::AngularApp::V1::UsersController < Api::AngularApp::V1::ApplicationController
  before_filter :authenticate_user!, only: [:me]
  before_filter :check_id_validation!, only: [:create]

  def create
    # requerimiento de BNA para recuperar los usuarios que fueron borrados, dejar que se vuelvan a registrar (BNA-309)
    user_register_params[:doc_number].present? ? @user = @current_store.customers.find_by(doc_number: user_register_params[:doc_number]) : @user = nil
    if @user&.deleted?
      @user.set_validator
      @user.update!(user_register_params.merge!(deleted_at: nil))
      render :show, status: :ok
    else
      @user = @current_store.customers.new(user_register_params.merge!(store_id: @current_store.id))
      @user.set_validator

      if @user.save!
        render :show, status: :ok
      else
        render json: {
          error: 'Un error ha ocurrido',
          errors: Mkp::Customer.bna_registration_validation(@current_store.id, @user.errors)
        }, status: :unprocessable_entity
      end
    end
  end

  def omniauth
    oauth = OmniauthService.call!(params[:provider], params[:token], @current_store)
    if oauth[:success]
      @user = oauth[:user]
      render json: { success: true, token: JsonWebToken.encode({ user_id: @user.id, store_id: @current_store.id }) },
             status: :ok
    else
      render json: { error: 'Something went wrong, please try again' }, status: :unauthorized
    end
  end

  def me
    @current_user_points_error = nil
    @current_user_points = 0
    begin
      @current_user_points = @current_user.points
    rescue Loyalty::ApiExceptions::ApiExceptionError => e
      @current_user_points_error = {"message_type"=>"text", "message"=>"En este momento, no se pueden visualizar tus puntos. Te invitamos a ingresar en unos minutos."}.to_json
    rescue StandardError => e
      @current_user_points_error = e.message
    end
  end

  def logout
    @current_user = nil
  end

  def validate_attributes
    return unless @current_store.id == 41

    user = @current_store.customers.new(user_register_params)

    # Prepare response with user validation
    response = {}

    if user.valid?
      response[:success] = true
      response[:user_valid] = true
    else
      response[:success] = false
      response[:user_valid] = false
      response[:error] = 'El email o DNI ya se encuentra registrado|Si considera esto un error, comunicate al 0810 4444 500 de lunes a sábados de 08 a 20 hs.'
    end

    # Add identity validation blocking status if doc_number is provided
    if user_register_params[:doc_number].present?
      blocking_result = IdentityValidation::AttemptBlockingService.call(
        doc_number: user_register_params[:doc_number],
        store_id: @current_store.id
      )

      if blocking_result.blocked?
        response[:identity_validation] = {
          blocked: true,
          reason: blocking_result.reason,
          message: blocking_result.message,
          type: blocking_result.reason == :temporary_daily_limit ? 'temporary' : 'permanent',
          can_proceed: false
        }
      else
        # Get current attempt count for frontend
        attempts_count = AnswerAttempt::AttemptedAnswer.where(
          store_id: @current_store.id,
          doc_number: user_register_params[:doc_number],
          created_at: 24.hours.ago..Time.current
        ).count

        response[:identity_validation] = {
          blocked: false,
          reason: nil,
          message: nil,
          type: 'none',
          can_proceed: true,
          attempts_today: attempts_count,
          attempts_remaining: [4 - attempts_count, 0].max
        }
      end
    end

    if response[:success]
      render json: response, status: :ok
    else
      render json: { error: 'El email o DNI ya se encuentra registrado|Si considera esto un error, comunicate al 0810 4444 500 de lunes a sábados de 08 a 20 hs.' }, status: :unprocessable_entity
    end
  end

  private

  def check_id_validation!
    if !RenaperAnswer.exists?(
      doc_number: user_register_params[:doc_number], answered: true, purchase_id: nil
    ) && @current_store.id == 41
      render json: { error: '401 Se requiere autorización' },
             status: :unauthorized
    end
  end

  def user_register_params
    params.require(:user).permit(:email, :password, :password_confirmation, :first_name,
                                 :last_name, :doc_type, :doc_number, :email, :telephone, :gender, :birthday_at, :cuit)
  end
end
