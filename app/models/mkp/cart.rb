module Mkp
  class Cart < ActiveRecord::Base
    extend Concerns::WiserPolymorphicBelongsTo

    # Allow to create cart with empty customers if we're creating from the api itself
    validates :customer_id, presence: true, uniqueness: { scope: [:customer_type, :network, :type ]}, if: Proc.new { |cart| cart.store.nil? }
    validates :purchase_id, uniqueness: true
    validate :purchase_id_against_orders

    belongs_to :coupon, class_name: 'Coupon::Network'
    belongs_to :customer, polymorphic: true
    belongs_to :store, foreign_key: :store_id, class_name: "Mkp::Store"
    belongs_to :address, class_name: "Mkp::Address"
    has_many   :items, class_name: CartItem
    has_many   :shops, through: :items
    has_many   :delivery_options, class_name: "Mkp::CartDeliveryOption"

    before_create :generate_purchase_id

    wiser_polymorphic_belongs_to :user,
                                 'User',
                                 'customer_id',
                                 'customer_type',
                                 table_name

    wiser_polymorphic_belongs_to :guest,
                                 'Mkp::Guest',
                                 'customer_id',
                                 'customer_type',
                                 table_name

    wiser_polymorphic_belongs_to :buyer,
                                 'Mkp::Customer',
                                 'customer_id',
                                 'customer_type',
                                 table_name


    scope :for_customer, ->(user) { where("customer_id = ? && customer_type = 'User'", user.id) }
    scope :with_address, -> { joins(:items).where('mkp_cart_items.address_id IS NOT NULL') }
    scope :by_purchase_id, ->(purchase_id) { where(purchase_id: purchase_id) }
    scope :updated_last_day, -> do
      joins(:items).
      where('mkp_carts.updated_at BETWEEN :start AND :end OR
             mkp_cart_items.updated_at BETWEEN :start AND :end',
             { start: 26.hours.ago, end: 10.minutes.ago} ).
      group('mkp_carts.id').
      order('mkp_carts.updated_at DESC')
    end
    scope :updated_recently, -> do
      joins(:items).
      where('mkp_carts.updated_at BETWEEN :start AND :end OR
             mkp_cart_items.updated_at BETWEEN :start AND :end',
             { start: 26.hours.ago, end: 1.seconds.ago} ).
      group('mkp_carts.id').
      order('mkp_carts.updated_at DESC')
    end

    scope :by_store_id, ->(store_id) { where(store_id: store_id) }

    scope :last_created, (lambda do |time_ago|
      where('mkp_carts.created_at > ?', time_ago)
    end)

    scope :not_empty, -> { joins(:items) }

    scope :with_customer, -> { where('customer_id IS NOT NULL') }

    scope :with_items, -> { joins(:items).group('mkp_carts.id') }

    scope :cart_ids_with_warranties, -> { joins(items: { variant: :product })
                                          .where(mkp_products: { transaction_type:5 } )
                                          .order(id: :desc).select(:id)
                                          .distinct }

    scope :without_warranties, -> { where.not(id: Mkp::Cart.cart_ids_with_warranties) }

    store :data, accessors: [
      :affiliate_id,
      :affiliate_type,
      :affiliated_at,
      :left_cart,
      :replica_id,
      :promotion
    ]

    # Returns a single instance kind of Mkp::Cart,
    # having the following logic
    # Mkp::QuickCart has priority if created_at < 1 hour ago
    # otherwise Mkp::Cart

    def self.cart(carts, network)
      carts.select { |c| c.network == network }.sort.first
    end

    # @params user the user for wich we create or get the cart
    # @params network scoped to network
    # EM: TODO refeactor this with the same method
    # from Mkp::QuickOrder
    def self.get_or_create_cart_for(customer, network)
      customer.reload.cart(network) or customer.carts.create(network: network)
    end

    def <=>(other)
      if self.class == other.class
        0
      elsif instance_of?(QuickCart) && ! expired?
        -1
      else
        1
      end
    end

    def apply_affiliate(affiliate, id)
      return if affiliate_id == id && affiliate_type = affiliate
      self.affiliate_id   = id
      self.affiliate_type = affiliate
      self.affiliated_at  = Time.now
      save
    end

    def coupon_discount
      DiscountHandler.determine_amount(self)
    end

    # Cart never expires
    def expired?
      false
    end

    def subtotal
      subtotal_price = 0

      items.each do |item|
        points_value = item.product.points_value_by_store(store, item.points)
        subtotal_price += item.total - points_value
      end

      subtotal_price > 0 ? subtotal_price : 0
    end

    def title
      return '' unless items.present?
      PurchaseTitleizer.perform(items.first.variant.try(:title),
                                items.size,
                                network)
    end

    def total
      _total = subtotal
      _total -= coupon_discount
      _total += taxes
      _total += get_shipping_cost
      _total > 0 ? _total.to_f.round(2) : 0
    end

    def have_taxes?(state = nil)
      taxes(state) > 0
    end

    def taxes(state = nil)
      return 0.0 unless state.present?
      shops.map do |shop|
        if shop.charge_tax_to?(state)
          amount_to_tax = subtotal - coupon_discount
          shop.tax_rate_for_state(state) * amount_to_tax / 100.0
        else
          0
        end
      end.sum.to_f.round(2)
    end

    def valid_affiliate?
      affiliated_at && affiliated_at + 5.days > Time.now
    end

    def update_items(cart_items)
      merge_items(cart_items)
      remove_items_not_present_in(cart_items)
    end

    def merge_items(new_items)
      create_or_update_items(new_items)
    end

    def has_coupon?
      coupon.present?
    end

    def remove_items_with_variant_ids(ids)
      items.each do |item|
        item.destroy if ids.include?(item.variant_id)
      end
    end

    def items_per_shop
      items.group_by { |item| item.shop }
    end

    def shipments
      items.group_by { |item| item.shop.fulfillment_shop }
    end

    def reset_status!
      self.update_attributes(status: nil)
    end

    def set_checkout_status!
      self.update_attributes(status: 'checkout')
    end

    def entered_checkout?
      status == 'checkout'
    end

    def items_to_checkout
      items.map{|c| {
        variant_id: c.variant_id,
        quantity: c.quantity,
        points: c.points
      }}
    end

    def checkout_items
      items
    end

    def build_delivery_options(checkout_cart)
      suborders = Mkp::PurchaseProcessor.send(:suborder_generator, checkout_cart)
      suborders.each do |suborder|
        next if virtual_delivery(suborder)
        # preguntar por el producto, si es type 5 , no tiene delivery

        # Check if suborder has points products (only once)
        has_points = has_points_products?(suborder)

        if has_points
          options = [build_points_delivery_option]
        elsif suborder.shop.delivery_by_matrix
          options = checkout_cart.store.get_delivery_options(address.zip, [suborder.get_suborder_weight], [suborder.shop.id])
        else
          service = Gateways::Shipments::Krabpack::SuborderDeliveryOptionsService.new(suborder: suborder, address: address, checkout_cart: checkout_cart)
          service.perform

          options = service.valid ? service.response : checkout_cart.store.get_delivery_options(address.zip, [suborder.get_suborder_weight], [suborder.shop.id])
        end

        Rails.logger.info "Coati: Cotizar envio para Shop #{checkout_cart&.shops.first.title}"
        # Genero los productos y paquetes para coati
        coati_products = checkout_items.map do |checkout_item|
          coati_product = Gateways::Shipments::Coati::CoatiProduct.new(
            sku: checkout_item.variant.gp_sku || nil,
            name: checkout_item.variant.title || nil,
            price: checkout_item.variant.product.price || 0,
            quantity: checkout_item.quantity || 0
          )
          coati_product.packages = checkout_item.variant.product.packages.map do |package|
            p = Gateways::Shipments::Coati::Package.new
            p.width(package[:width], package[:length_unit])
            p.height(package[:height], package[:length_unit])
            p.depth(package[:length], package[:length_unit])
            p.weight(package[:weight], package[:mass_unit])
            p
          end
          coati_product
        end

        options.concat Gateways::Shipments::Coati::Manager.quote_shipping(
          store: store,
          cuit: checkout_cart&.shops.first.cuit || nil,
          originZipCode: checkout_cart&.shops.first&.warehouses.first.zip || nil,
          destinationZipCode: address.zip || nil,
          products: coati_products
        )
        options = final_delivery_options(options, suborder)

        Rails.logger.info "--- FINAL OPTIONS: #{options} ----"
        options.each do |option|
          delivery_option = delivery_options.create(option)
          # Auto-select delivery option for points products (using cached result)
          delivery_option.update(selected: true) if has_points
          Rails.logger.info "-- Delivery Option created: #{delivery_options.map(&:id)}"
        end
      end
    end

    def virtual_delivery(suborder)
      transaction_type = suborder.items.first.product.transaction_type
      transaction_type == "warranties"
    end

    def has_points_products?(suborder)
      suborder.items.any? { |item| item.product.transaction_type == 'points' }
    end

    def build_points_delivery_option
      {
        'price' => 0,
        'carrier' => 'Envío gratuito',
        'service' => 'Productos con puntos',
        'mode' => 'Envío gratuito para productos con puntos',
        'quote_item_id' => nil
      }
    end

    def final_delivery_options(options, suborder)
      shop = suborder.shop
      filtered_options = filtered_options(options, shop.id)
      Rails.logger.info "- IS PICKEABLE? #{pickable?(suborder.items)}"
      Rails.logger.info "- Shop PICKUP? #{shop.allows_pickup}"
      return filtered_options unless shop.allows_pickup && pickable?(suborder.items)

      [filtered_options, pickup_options(shop)].flatten
    end

    def allows_pickup(suborder)
      suborder.shop.allows_pickup && pickable?(suborder.items)
    end

    def pickable?(items)
      items.map { |it| it.variant.product }.map(&:pickeable?).all?
    end

    def pickup_options(shop)
      pickup_options = []
      shop.warehouses.select(&:pickup).each do |warehouse|
        pickup_options << {
          title: "Retiro por sucursal: #{warehouse.open_hours} - #{warehouse.closing_hours}",
          pickup_address: "#{warehouse.address}, #{warehouse.city}, #{warehouse.state}, CP #{warehouse.zip}",
          service_level: 'Pickup',
          shop_id: shop.id,
          carrier: shop.title,
          pickup: true
        }
      end
      pickup_options
    end

    def filtered_options(options, shop_id)
      filtered_options = []
      options.each do |option|
        next if option['price'].blank?

        filtered_options << {
          charge: option['price'].round(2),
          title: "#{option['carrier']} - #{option['service']}",
          service_level: option['service'],
          carrier: option['carrier'],
          shop_id: shop_id,
          # Agrego option['service_level_real'] para que funcione con Coati.
          service_level_real: option['mode'] || option['service_level_real'],
          external_shipment_id: option['quote_item_id']
        }
      end
      filtered_options
    end

    def get_delivery_options
      result = []

      delivery_options.group_by(&:shop_id).each do |key, value|
        shop_items = items.select { |item| item.shop.id == key }
        next unless shop_items.any?

        title = shop_items.first.title
        title += " + #{shop_items.count - 1} productos" if shop_items.count > 1
        result << {
          shop_id: key,
          title: title,
          options: value
        }
      end
      result
    end

    def get_selected_delivery_options
      delivery_options.selected.group_by(&:shop_id)
    end

    def get_shipping_cost
      selected_delivery_options = get_selected_delivery_options
      return 0 unless selected_delivery_options.any?
      selected_delivery_options.sum{|key, value| value.sum(&:charge)}
    end

    def customer_uuid
      customer.uuid
    end

    def full_points?
      checkout_items.all? { |item| item.product.transaction_type == 'points' }
    end

    private

    def create_or_update_items(cart_items)
      cart_items.each do |cart_item|
        create_or_update_item(cart_item[:variant_id], cart_item[:quantity], cart_item[:points] || 0)
      end
    end

    def create_or_update_item(variant_id, quantity, points)
      return if quantity.to_i == 0 || (variant = Variant.find_by_id(variant_id)).blank?

      item = items.select { |i| i.variant_id == variant_id.to_i }.first

      if item
        item.update_quantity_and_attrs(quantity, variant)
      else
        items.create!(build_cart_item_params(variant, quantity, points))
      end
    end

    def build_cart_item_params(variant, quantity, points)
      {
        variant: variant,
        quantity: quantity,
        price: variant.product.regular_price,
        currency: variant.currency
      }.tap do |hash|
        hash[:points] = points if points.to_i > 0
      end
    end

    def remove_items_not_present_in(cart_items)
      ids = cart_items.map { |i| i[:variant_id].to_i }

      items.each do |item|
        item.destroy unless ids.include?(item.variant_id.to_i)
      end
      reload
    end

    def purchase_id_against_orders
      errors.add(:purchase_id, "duplicated in orders") if Mkp::Order.exists?(purchase_id: self.purchase_id)
    end

    def generate_purchase_id
      begin
        self.purchase_id = SecureRandom.urlsafe_base64(nil, false)
      end while self.class.exists?(purchase_id: self.purchase_id) || Mkp::Order.exists?(purchase_id: self.purchase_id)
    end
  end
end
